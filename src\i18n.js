// src/i18n.js أو app/i18n.js

import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import HttpBackend from 'i18next-http-backend';

i18n
  .use(HttpBackend) // تحميل ملفات الترجمة من مجلد locales
  .use(LanguageDetector) // كشف لغة المتصفح أو الاختيار اليدوي
  .use(initReactI18next) // ربط مع React
  .init({
    fallbackLng: 'en', // اللغة الافتراضية
    supportedLngs: ['en', 'ar', 'it', 'es', 'de'], // اللغات المتوفرة
    debug: false,
    interpolation: {
      escapeValue: false, // React آمن من XSS
    },
    backend: {
      loadPath: '/locales/{{lng}}/common.json', // مسار ملفات الترجمة
    },
    detection: {
      order: ['localStorage', 'cookie', 'navigator', 'htmlTag'],
      caches: ['localStorage', 'cookie'],
    },
  });

export default i18n;
