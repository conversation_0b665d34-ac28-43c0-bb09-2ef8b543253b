"use client";

import { ReactNode, useEffect } from "react";
import { I18nextProvider } from "react-i18next";
import { i18n } from "../i18n";

interface I18nProviderProps {
  children: ReactNode;
}

export default function I18nProvider({ children }: I18nProviderProps) {
  useEffect(() => {
    // ممكن تحط أي تهيئة إضافية لو حابب
  }, []);

  return <I18nextProvider i18n={i18n}>{children}</I18nextProvider>;
}
