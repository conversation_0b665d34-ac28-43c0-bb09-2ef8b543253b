"use client";

import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { useTranslation } from "react-i18next";
import "swiper/css";

export default function Home() {
  const { t } = useTranslation();
  const [time, setTime] = useState({
    seconds: 0,
    minutes: 0,
    hours: 0,
    days: 0,
    weeks: 0,
    months: 0,
  });

  const [showInfo, setShowInfo] = useState(false);

  useEffect(() => {
    const updateTime = () => {
      // Conference date: November 25, 2025
      const conferenceDate = new Date('2025-11-25T00:00:00');
      const now = new Date();
      const diff = conferenceDate.getTime() - now.getTime();

      // If conference has passed, show zeros
      if (diff <= 0) {
        setTime({ seconds: 0, minutes: 0, hours: 0, days: 0, weeks: 0, months: 0 });
        return;
      }

      // Calculate months and remaining days more accurately
      const currentDate = new Date(now);
      let months = 0;
      const tempDate = new Date(currentDate);

      // Count full months
      while (tempDate.getFullYear() < conferenceDate.getFullYear() ||
             (tempDate.getFullYear() === conferenceDate.getFullYear() &&
              tempDate.getMonth() < conferenceDate.getMonth())) {
        months++;
        tempDate.setMonth(tempDate.getMonth() + 1);
      }

      // Calculate remaining time after months
      const remainingTime = conferenceDate.getTime() - tempDate.getTime();
      const remainingDays = Math.floor(remainingTime / (1000 * 60 * 60 * 24));
      const weeks = Math.floor(remainingDays / 7);
      const days = remainingDays % 7;

      const hours = Math.floor((remainingTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((remainingTime % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((remainingTime % (1000 * 60)) / 1000);

      setTime({ seconds, minutes, hours, days, weeks, months });
    };

    updateTime();

    const timer = setInterval(updateTime, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="relative w-full text-black overflow-hidden bg-white min-h-screen">
      {/* خلفية الصورة */}
      <div className="relative w-full h-[500px] sm:h-[550px] md:h-[600px]">
        <Image
          src="/photo1.png"
          alt="Conference"
          fill
          style={{ objectFit: "cover" }}
          className="z-0"
          priority
        />
        <div className="absolute inset-0 bg-black/40 z-10" />
        <div className="relative z-20 flex flex-col items-center justify-center h-full text-center px-4 max-w-4xl mx-auto">
          <h1 className="text-3xl md:text-5xl font-bold leading-snug text-white">
            Pioneering Sustainable{" "}
            <span className="bg-orange-500 px-2 py-1 rounded text-white inline-block">
              Energy
            </span>{" "}
            for a Greener Future
          </h1>
          <p className="mt-6 text-lg md:text-xl max-w-xl text-white">
            Join global experts, researchers, and policymakers in Tripoli to
            explore innovations in renewable energy, oil & gas, and climate
            solutions.
          </p>
          <div className="mt-8 flex gap-4 justify-center">
            <Link
              href="/registration"
              className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-md font-semibold transition"
            >
              {t("register")}
            </Link>
            <Link
              href="/submission"
              className="bg-transparent border border-white hover:border-orange-500 hover:text-orange-500 text-white px-6 py-2 rounded-md font-semibold transition"
            >
              Submit Your Paper
            </Link>
          </div>
        </div>
      </div>

      {/* العداد */}
      <div className="mt-6 text-center">
        <span className="text-black text-xl font-bold">Conference </span>
        <span className="text-orange-500 text-xl font-semibold">Countdown</span>
      </div>
      <div className="mt-6 flex justify-center gap-6 px-4 flex-wrap max-w-4xl mx-auto">
        {[
          { label: t("seconds"), value: time.seconds },
          { label: t("minutes"), value: time.minutes },
          { label: t("hours"), value: time.hours },
          { label: t("days"), value: time.days },
          { label: t("weeks"), value: time.weeks },
          { label: t("months"), value: time.months },
        ].map(({ label, value }) => (
          <div
            key={label}
            className="bg-orange-500 text-white w-20 h-24 rounded-md flex flex-col items-center justify-center shadow-lg"
          >
            <span className="text-3xl font-bold">{value}</span>
            <span className="text-sm mt-1">{label}</span>
          </div>
        ))}
      </div>

      {/* جملة READ MORE و معلومات المؤتمر */}
      <div className="mt-10 text-center px-4 max-w-3xl mx-auto">
        <p className="text-lg md:text-xl font-semibold">
          Get to know about{" "}
          <span className="text-orange-500 uppercase font-bold">CONFERENCE</span>
        </p>
        <button
          onClick={() => setShowInfo(!showInfo)}
          className=" mt-4 bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-md font-semibold transition"
        >
          {t("readMore")}
        </button>

        {showInfo && (
          <div className="mt-6 text-left bg-gray-100 p-6 rounded-md shadow-md text-gray-900 text-base md:text-lg">
            The Renewable Energy, Gas & Oil, and Climate Change Conference will
            take place in Tripoli, Libya, in November 2025, bringing together
            experts, researchers, industry leaders, and policymakers to address
            pressing energy and environmental challenges. This pioneering event
            aims to bridge the gap between academia, industry, and government
            by fostering dialogue and collaboration on innovative solutions in
            renewable energy, oil and gas, and climate change mitigation.
          </div>
        )}
      </div>

      {/* Scientific Journals Section */}
      <div className="mt-16 text-center px-4">
  <h2 className="text-2xl md:text-3xl font-bold mb-8">
    <span className="text-orange-500">Scientific</span> Journals
  </h2>

  <Swiper
    spaceBetween={20}
    slidesPerView={1.2}
    breakpoints={{
      640: { slidesPerView: 2 },
      1024: { slidesPerView: 3 },
    }}
  >
    {/* المجلة 1 */}
    <SwiperSlide>
      <div className="flex flex-col items-center">
        <Image
          src="/photo2.png"
          alt="Libya Journal"
          width={120}
          height={120}
          className="rounded shadow-md"
        />
        <p className="mt-4 text-center font-medium">
          Libya Journal for Applied Sciences and Technology
        </p>
      </div>
    </SwiperSlide>

    {/* المجلة 2 */}
    <SwiperSlide>
      <div className="flex flex-col items-center">
        <Image
          src="/photo3.png"
          alt="Solar Journal"
          width={120}
          height={120}
          className="rounded shadow-md"
        />
        <p className="mt-4 text-center font-medium">
          Solar Energy and Sustainable Development Journal
        </p>
      </div>
    </SwiperSlide>

    {/* المجلة 3 */}
    <SwiperSlide>
      <div className="flex flex-col items-center">
        <Image
          src="/photo4.png"
          alt="Pure Sciences Journal"
          width={120}
          height={120}
          className="rounded shadow-md"
        />
        <p className="mt-4 text-center font-medium">
          Pure and Applied Sciences Journal
        </p>
      </div>
    </SwiperSlide>
  </Swiper>
</div>
{/* Submit Your Research Section */}
<div className="mt-20 px-4 w-full flex flex-col items-center text-center">
  <h2 className="text-lg md:text-2xl font-bold mb-4">
    Submit Your <span className="text-orange-500">Research</span>
  </h2>

  <p className="text-gray-700 text-sm md:text-base mb-8 max-w-2xl">
    Share your innovative research and contribute to the advancement of sustainable energy solutions.
  </p>

  {/* اجعل الحاوية أفقيّة */}
  <div className="flex flex-row flex-wrap justify-center gap-8 max-w-xl mx-auto text-left">
    {/* العمود الأيسر */}
    <div className="flex flex-col gap-4 min-w-[140px]">
      <div className="flex items-center gap-2 text-xs sm:text-sm">
        <i className="fas fa-solar-panel text-orange-500"></i>
        <span className="font-medium">Renewable Energy</span>
      </div>
      <div className="flex items-center gap-2 text-xs sm:text-sm">
        <i className="fas fa-globe-americas text-orange-500"></i>
        <span className="font-medium">Climate Change</span>
      </div>
    </div>

    {/* العمود الأيمن */}
    <div className="flex flex-col gap-4 min-w-[140px]">
      <div className="flex items-center gap-2 text-xs sm:text-sm">
        <i className="fas fa-industry text-orange-500"></i>
        <span className="font-medium">Oil &amp; Gas Transition</span>
      </div>
      <div className="flex items-center gap-2 text-xs sm:text-sm">
        <i className="fas fa-leaf text-orange-500"></i>
        <span className="font-medium">Sustainable Development</span>
      </div>
    </div>
  </div>
</div>

<div className="mt-10 flex justify-center">
  <button className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-md font-semibold transition flex items-center gap-2">
    <i className="fas fa-file-upload"></i>
     <input type="file" /> Submit Your Paper
  </button>
</div>
<h2 className="text-2xl md:text-3xl font-bold text-center mt-16">
  <span className="text-orange-500">Conference</span> Timeline
</h2>
<div className="mt-10 flex flex-row flex-wrap items-center justify-center gap-12 px-4 max-w-5xl mx-auto">
  {/* النقطة 1 */}
  <div className="flex flex-col items-center min-w-[150px]">
    <div className="bg-orange-500 text-white text-sm px-4 py-1 rounded-full shadow-md mb-2">
      June 15, 2025
    </div>
    <div className="w-4 h-4 bg-orange-500 rounded-full shadow-lg mb-2" />
    <p className="text-center text-sm md:text-base font-medium">Abstract Submission Deadline</p>
  </div>

  {/* النقطة 2 */}
  <div className="flex flex-col items-center min-w-[150px]">
    <div className="w-4 h-4 bg-orange-500 rounded-full shadow-lg mb-2" />
    <p className="text-center text-sm md:text-base font-medium">Notification of Acceptance</p>
    <div className="bg-orange-500 text-white text-sm px-4 py-1 rounded-full shadow-md mt-2">
      August 15, 2025
    </div>
  </div>

  {/* النقطة 3 */}
  <div className="flex flex-col items-center min-w-[150px]">
    <div className="bg-orange-500 text-white text-sm px-4 py-1 rounded-full shadow-md mb-2">
      October 1, 2025
    </div>
    <div className="w-4 h-4 bg-orange-500 rounded-full shadow-lg mb-2" />
    <p className="text-center text-sm md:text-base font-medium">Final Paper Submission</p>
  </div>

  {/* النقطة 4 */}
  <div className="flex flex-col items-center min-w-[150px]">
    <div className="w-4 h-4 bg-orange-500 rounded-full shadow-lg mb-2" />
    <p className="text-center text-sm md:text-base font-medium">Conference Dates</p>
    <div className="bg-orange-500 text-white text-sm px-4 py-1 rounded-full shadow-md mt-2">
      November 25–27, 2025
    </div>
  </div>
</div>
<div className="mt-20 text-center px-4">
  <h2 className="text-2xl md:text-3xl font-bold">
    Our Official <span className="text-orange-500">Sponsors</span>
  </h2>
</div>
 <div className="mt-10 px-4 max-w-6xl mx-auto">
  <Swiper
    spaceBetween={20}
    slidesPerView={2}
    breakpoints={{
      640: { slidesPerView: 2 },
      768: { slidesPerView: 3 },
      1024: { slidesPerView: 4 },
    }}
  >
    {["photo5.png", "photo6.png", "photo7.png", "photo8.png"].map((src, index) => (
      <SwiperSlide key={index} className="flex justify-center">
        <Image
          src={`/${src}`}
          alt={`Sponsor ${index + 1}`}
          width={120}
          height={120}
          className="rounded shadow-md object-contain"
        />
      </SwiperSlide>
    ))}
  </Swiper>
</div>
<div id="contact" className="mt-24 px-4 max-w-4xl mx-auto">

  {/* العنوان والوصف */}
  <div className="  text-center mb-10">
    <h2 className="text-3xl md:text-4xl font-bold mb-2">
      <span className="text-orange-500">Contact</span> Us
    </h2>
    <p className="text-gray-600 text-base md:text-lg">
      Service description example: To buy a plot to build your house, this requires documenting
      the sale and purchase process in the notarial offices to register the property in your name.
    </p>
  </div>

  {/* النموذج */}
  <form className="grid grid-cols-1 md:grid-cols-2 gap-6">
    {/* الاسم */}
    <div className="flex items-center border border-gray-300 rounded-md px-4 py-3">
      <i className="fas fa-user text-orange-500 mr-3" />
      <input
        type="text"
        placeholder="Full Name"
        className="w-full outline-none text-sm"
      />
    </div>

    {/* الإيميل */}
    <div className="flex items-center border border-gray-300 rounded-md px-4 py-3">
      <i className="fas fa-envelope text-orange-500 mr-3" />
      <input
        type="email"
        placeholder="Email"
        className="w-full outline-none text-sm"
      />
    </div>

    {/* الموضوع */}
    <div className="md:col-span-2 flex items-center border border-gray-300 rounded-md px-4 py-3">
      <i className="fas fa-tag text-orange-500 mr-3" />
      <input
        type="text"
        placeholder="Subject"
        className="w-full outline-none text-sm"
      />
    </div>

    {/* الرسالة */}
    <div className="md:col-span-2 flex border border-gray-300 rounded-md px-4 py-3">
      <i className="fas fa-comment-dots text-orange-500 mr-3 mt-1" />
      <textarea
        placeholder="Your Message"
        className="w-full outline-none text-sm resize-none min-h-[120px]"
      />
    </div>

    {/* زر الإرسال */}
    <div className="md:col-span-2 text-center">
      <button
        type="submit"
        className="bg-orange-500 hover:bg-orange-600 text-white font-semibold px-8 py-2 rounded-md transition mb-12"
      >
        Send Message
      </button>
    </div>
  </form>
</div>

    </div>
  );
}
