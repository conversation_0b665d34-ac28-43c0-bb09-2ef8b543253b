"use client";

import { useState, useRef, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { Menu, X } from "lucide-react";
import { useUser } from "../context/UserContext";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import i18n from "../i18n";

function Navbar() {
  const [menuOpen, setMenuOpen] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const toggleMenu = () => setMenuOpen(!menuOpen);
  const { user, setUser, isLoading } = useUser();
  const router = useRouter();
  const dropdownRef = useRef(null);
  const { t } = useTranslation();

  const handleLanguageChange = (lang) => {
    i18n.changeLanguage(lang);
    setMenuOpen(false);
  };

  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdownOpen(false);
      }
    }
    if (dropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [dropdownOpen]);

  const handleLogout = () => {
    setUser(null);
    setDropdownOpen(false);
    router.push("/login");
  };

  return (
    <nav className="absolute top-[35px] left-0 w-full bg-gray-800/30 text-white z-40 backdrop-blur-md border-b border-white/10">
      <div className="max-w-7xl mx-auto px-4 py-3 flex items-center justify-between h-16">
        <div className="flex items-center space-x-2 min-w-[60px]">
          <Image src="/logo.png" alt="Logo" width={50} height={40} />
        </div>

        <ul className="hidden md:flex space-x-4 text-sm uppercase font-medium flex-grow justify-center">
          <li><Link className="hover:text-orange-400 transition" href="/">{t("home")}</Link></li>
          <li><Link className="hover:text-orange-400 transition" href="/about">{t("about")}</Link></li>
          <li><Link className="hover:text-orange-400 transition" href="/committees">{t("committees")}</Link></li>
          <li><Link className="hover:text-orange-400 transition" href="/call-for-papers">{t("callForPapers")}</Link></li>
          <li><Link className="hover:text-orange-400 transition" href="/submission">{t("submission")}</Link></li>
          <li><Link className="hover:text-orange-400 transition" href="/registration">{t("registration")}</Link></li>
          <li><Link className="hover:text-orange-400 transition" href="/venue">{t("venue")}</Link></li>
          <li>
            <Link href="/contact-us" scroll={true} className="block bg-orange-500 hover:bg-orange-600 text-white px-3 py-1 rounded-md text-sm font-semibold">
              {t("contact")}
            </Link>
          </li>
        </ul>

        <div className="flex items-center space-x-3 min-w-[100px] justify-end relative">
          <select onChange={(e) => handleLanguageChange(e.target.value)} className="bg-transparent border border-white text-white text-sm rounded px-1 py-0.5">
            <option value="en">EN</option>
            <option value="ar">AR</option>
            <option value="it">IT</option>
            <option value="es">ES</option>
            <option value="de">DE</option>
          </select>

          {isLoading ? (
            <div className="w-9 h-9 rounded-full bg-gray-300 animate-pulse"></div>
          ) : user ? (
            <>
              <button onClick={() => setDropdownOpen(!dropdownOpen)} className="focus:outline-none">
                <Image src={user.photoURL || "/avatar.png"} alt="User" width={36} height={36} className="rounded-full border border-white" />
              </button>
              {dropdownOpen && (
                <div ref={dropdownRef} className="absolute right-0 mt-2 w-40 bg-gray-900 border border-gray-700 rounded shadow-lg z-50">
                  <div className="px-4 py-2 border-b border-gray-700">
                    <p className="text-sm text-white font-medium">{user.name}</p>
                    <p className="text-xs text-gray-400">{user.email}</p>
                  </div>
                  <button onClick={handleLogout} className="block w-full text-left px-4 py-2 text-sm hover:bg-orange-600 hover:text-white text-red-500">
                    {t("logout")}
                  </button>
                </div>
              )}
            </>
          ) : (
            <Link href="/login" className="text-sm bg-orange-500 hover:bg-orange-600 px-3 py-1 rounded text-white transition-colors">
              {t("login")}
            </Link>
          )}

          <button className="md:hidden text-white" onClick={toggleMenu} aria-label="Toggle menu">
            {menuOpen ? <X size={28} /> : <Menu size={28} />}
          </button>
        </div>
      </div>

      {menuOpen && (
        <div className="md:hidden flex flex-col px-6 pb-4 pt-2 space-y-4 bg-gray-800/30 text-white backdrop-blur-md border-t border-white/10">
          <Link className="hover:text-orange-400 transition" href="/" onClick={toggleMenu}>{t("home")}</Link>
          <Link className="hover:text-orange-400 transition" href="/about" onClick={toggleMenu}>{t("about")}</Link>
          <Link className="hover:text-orange-400 transition" href="/committees" onClick={toggleMenu}>{t("committees")}</Link>
          <Link className="hover:text-orange-400 transition" href="/call-for-papers" onClick={toggleMenu}>{t("callForPapers")}</Link>
          <Link className="hover:text-orange-400 transition" href="/submission" onClick={toggleMenu}>{t("submission")}</Link>
          <Link className="hover:text-orange-400 transition" href="/registration" onClick={toggleMenu}>{t("registration")}</Link>
          <Link className="hover:text-orange-400 transition" href="/venue" onClick={toggleMenu}>{t("venue")}</Link>
          <Link href="/contact-us" onClick={toggleMenu} className="block bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md text-sm font-semibold w-fit">
            {t("contact")}
          </Link>
        </div>
      )}
    </nav>
  );
}

export default Navbar;
