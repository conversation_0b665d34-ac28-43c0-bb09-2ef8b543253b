"use client";

import { useState, useRef, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { Menu, X, ChevronDown } from "lucide-react";
import { useUser } from "../context/UserContext";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import i18n from '../i18n';








function Navbar() {
  const [menuOpen, setMenuOpen] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [languageDropdownOpen, setLanguageDropdownOpen] = useState(false);
  const toggleMenu = () => setMenuOpen(!menuOpen);
  const { user, setUser, isLoading } = useUser();
  const router = useRouter();
  const dropdownRef = useRef<HTMLDivElement>(null);
  const languageDropdownRef = useRef<HTMLDivElement>(null);
  const { t, i18n: i18nInstance } = useTranslation();

  const languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'ar', name: 'العربية', flag: '🇱🇾' },
    { code: 'it', name: 'Italiano', flag: '🇮🇹' },
    { code: 'es', name: 'Español', flag: '🇪🇸' },
    { code: 'de', name: 'Deutsch', flag: '🇩🇪' }
  ];

  const currentLanguage = languages.find(lang => lang.code === i18nInstance.language) || languages[0];

  const handleLanguageChange = (lang: string) => {
    i18n.changeLanguage(lang);
    setLanguageDropdownOpen(false);
    setMenuOpen(false);
  };

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDropdownOpen(false);
      }
      if (languageDropdownRef.current && !languageDropdownRef.current.contains(event.target as Node)) {
        setLanguageDropdownOpen(false);
      }
    }
    if (dropdownOpen || languageDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [dropdownOpen, languageDropdownOpen]);

  const handleLogout = () => {
    setUser(null);
    setDropdownOpen(false);
    router.push("/login");
  };

  return (
    <nav className="absolute top-[35px] left-0 w-full bg-gray-800/30 text-white z-40 backdrop-blur-md border-b border-white/10">
      <div className="max-w-7xl mx-auto px-4 py-3 flex items-center justify-between h-16">
        <div className="flex items-center space-x-2 min-w-[60px]">
          <Image src="/logo.png" alt="Logo" width={50} height={40} />
        </div>

        <ul className="hidden md:flex space-x-4 text-sm uppercase font-medium flex-grow justify-center">
          <li><Link className="hover:text-orange-400 transition" href="/">{t("home")}</Link></li>
          <li><Link className="hover:text-orange-400 transition" href="/about">{t("about")}</Link></li>
          <li><Link className="hover:text-orange-400 transition" href="/committees">{t("committees")}</Link></li>
          <li><Link className="hover:text-orange-400 transition" href="/call-for-papers">{t("callForPapers")}</Link></li>
          <li><Link className="hover:text-orange-400 transition" href="/submission">{t("submission")}</Link></li>
          <li><Link className="hover:text-orange-400 transition" href="/registration">{t("registration")}</Link></li>
          <li><Link className="hover:text-orange-400 transition" href="/venue">{t("venue")}</Link></li>
          <li>
            <Link href="/contact-us" scroll={true} className="block bg-orange-500 hover:bg-orange-600 text-white px-3 py-1 rounded-md text-sm font-semibold">
              {t("contact")}
            </Link>
          </li>
        </ul>

        <div className="flex items-center space-x-3 min-w-[100px] justify-end relative">
          {/* Language Dropdown */}
          <div className="relative" ref={languageDropdownRef}>
            <button
              onClick={() => setLanguageDropdownOpen(!languageDropdownOpen)}
              className="flex items-center space-x-1 bg-transparent border border-white text-white text-sm rounded px-2 py-1 hover:bg-white/10 transition-colors"
            >
              <span>{currentLanguage.flag}</span>
              <span>{currentLanguage.code.toUpperCase()}</span>
              <ChevronDown size={14} />
            </button>

            {languageDropdownOpen && (
              <div className="absolute right-0 mt-2 w-40 bg-gray-900 border border-gray-700 rounded shadow-lg z-50">
                {languages.map((lang) => (
                  <button
                    key={lang.code}
                    onClick={() => handleLanguageChange(lang.code)}
                    className={`w-full text-left px-3 py-2 text-sm hover:bg-orange-600 hover:text-white transition-colors flex items-center space-x-2 ${
                      i18nInstance.language === lang.code ? 'bg-orange-500 text-white' : 'text-gray-300'
                    }`}
                  >
                    <span>{lang.flag}</span>
                    <span>{lang.name}</span>
                  </button>
                ))}
              </div>
            )}
          </div>

          {isLoading ? (
            <div className="w-9 h-9 rounded-full bg-gray-300 animate-pulse"></div>
          ) : user ? (
            <>
              <button onClick={() => setDropdownOpen(!dropdownOpen)} className="focus:outline-none">
                <Image src={user.photoURL || "/avatar.png"} alt="User" width={36} height={36} className="rounded-full border border-white" />
              </button>
              {dropdownOpen && (
                <div ref={dropdownRef} className="absolute right-0 mt-2 w-40 bg-gray-900 border border-gray-700 rounded shadow-lg z-50">
                  <div className="px-4 py-2 border-b border-gray-700">
                    <p className="text-sm text-white font-medium">{user.name}</p>
                    <p className="text-xs text-gray-400">{user.email}</p>
                  </div>
                  <button onClick={handleLogout} className="block w-full text-left px-4 py-2 text-sm hover:bg-orange-600 hover:text-white text-red-500">
                    {t("logout")}
                  </button>
                </div>
              )}
            </>
          ) : (
            <Link href="/login" className="text-sm bg-orange-500 hover:bg-orange-600 px-3 py-1 rounded text-white transition-colors">
              {t("login")}
            </Link>
          )}

          <button className="md:hidden text-white" onClick={toggleMenu} aria-label="Toggle menu">
            {menuOpen ? <X size={28} /> : <Menu size={28} />}
          </button>
        </div>
      </div>

      {menuOpen && (
        <div className="md:hidden flex flex-col px-6 pb-4 pt-2 space-y-4 bg-gray-800/30 text-white backdrop-blur-md border-t border-white/10">
          <Link className="hover:text-orange-400 transition" href="/" onClick={toggleMenu}>{t("home")}</Link>
          <Link className="hover:text-orange-400 transition" href="/about" onClick={toggleMenu}>{t("about")}</Link>
          <Link className="hover:text-orange-400 transition" href="/committees" onClick={toggleMenu}>{t("committees")}</Link>
          <Link className="hover:text-orange-400 transition" href="/call-for-papers" onClick={toggleMenu}>{t("callForPapers")}</Link>
          <Link className="hover:text-orange-400 transition" href="/submission" onClick={toggleMenu}>{t("submission")}</Link>
          <Link className="hover:text-orange-400 transition" href="/registration" onClick={toggleMenu}>{t("registration")}</Link>
          <Link className="hover:text-orange-400 transition" href="/venue" onClick={toggleMenu}>{t("venue")}</Link>
          <Link href="/contact-us" onClick={toggleMenu} className="block bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md text-sm font-semibold w-fit">
            {t("contact")}
          </Link>

          {/* Language selection for mobile */}
          <div className="border-t border-white/20 pt-4">
            <p className="text-sm text-gray-300 mb-2">Language / اللغة</p>
            <div className="grid grid-cols-2 gap-2">
              {languages.map((lang) => (
                <button
                  key={lang.code}
                  onClick={() => handleLanguageChange(lang.code)}
                  className={`flex items-center space-x-2 px-3 py-2 rounded text-sm transition-colors ${
                    i18nInstance.language === lang.code
                      ? 'bg-orange-500 text-white'
                      : 'bg-white/10 text-gray-300 hover:bg-white/20'
                  }`}
                >
                  <span>{lang.flag}</span>
                  <span>{lang.name}</span>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </nav>
  );
}

export default Navbar;
