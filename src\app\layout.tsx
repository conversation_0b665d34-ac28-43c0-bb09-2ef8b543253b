import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import Navbar from "../components/Navbar";
import Footer from "../components/Footer";
import { UserProvider } from '../context/UserContext';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "International Conference 2025",
  description: "Renewable Energy, Gas & Oil, and Climate Change Conference",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col`}
      >
        {/* ✅ الشريط البرتقالي المتحرك */}
        <div className="bg-orange-600 overflow-hidden whitespace-nowrap">
          <div className="animate-marquee text-white text-sm py-2 px-4 inline-block">
            International Renewable Energy, Gas & Oil, and Climate Change Conference — November 25–27, 2025 — Tripoli, Libya
          </div>
        </div>

        <UserProvider>
          <Navbar />

          <main className="flex-grow">{children}</main>

          <Footer />
        </UserProvider>
      </body>
    </html>
  );
}
